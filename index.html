<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LeadGen Builder - Multipurpose Site Builder</title>
    <link rel="stylesheet" href="assets/css/builder.css">
    <link rel="stylesheet" href="assets/css/sections.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="builder-header">
        <div class="header-left">
            <div class="logo">
                <i class="fas fa-cube"></i>
                <span>LeadGen Builder</span>
            </div>
        </div>
        
        <div class="header-center">
            <div class="preview-modes">
                <button class="preview-btn active" data-mode="desktop">
                    <i class="fas fa-desktop"></i>
                </button>
                <button class="preview-btn" data-mode="tablet">
                    <i class="fas fa-tablet-alt"></i>
                </button>
                <button class="preview-btn" data-mode="mobile">
                    <i class="fas fa-mobile-alt"></i>
                </button>
            </div>
        </div>
        
        <div class="header-right">
            <button class="btn btn-secondary" id="importBtn">
                <i class="fas fa-upload"></i> Import
            </button>
            <button class="btn btn-secondary" id="exportBtn">
                <i class="fas fa-download"></i> Export
            </button>
            <button class="btn btn-primary" id="previewBtn">
                <i class="fas fa-eye"></i> Preview
            </button>
            <button class="btn btn-success" id="publishBtn">
                <i class="fas fa-rocket"></i> Publish
            </button>
        </div>
    </header>

    <!-- Main Builder Interface -->
    <div class="builder-container">
        <!-- Sidebar -->
        <aside class="builder-sidebar">
            <div class="sidebar-tabs">
                <button class="tab-btn active" data-tab="sections">
                    <i class="fas fa-th-large"></i>
                    <span>Sections</span>
                </button>
                <button class="tab-btn" data-tab="templates">
                    <i class="fas fa-file-alt"></i>
                    <span>Templates</span>
                </button>
                <button class="tab-btn" data-tab="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </button>
            </div>

            <!-- Sections Tab -->
            <div class="tab-content active" id="sections-tab">
                <div class="section-category">
                    <h3>Hero Sections</h3>
                    <div class="section-grid">
                        <div class="section-item" data-section="hero-1">
                            <div class="section-preview">
                                <div class="preview-hero"></div>
                            </div>
                            <span>Hero Banner</span>
                        </div>
                        <div class="section-item" data-section="hero-2">
                            <div class="section-preview">
                                <div class="preview-hero-split"></div>
                            </div>
                            <span>Hero Split</span>
                        </div>
                    </div>
                </div>

                <div class="section-category">
                    <h3>Content Sections</h3>
                    <div class="section-grid">
                        <div class="section-item" data-section="features-3col">
                            <div class="section-preview">
                                <div class="preview-features"></div>
                            </div>
                            <span>Features 3 Col</span>
                        </div>
                        <div class="section-item" data-section="about-us">
                            <div class="section-preview">
                                <div class="preview-about"></div>
                            </div>
                            <span>About Us</span>
                        </div>
                        <div class="section-item" data-section="testimonials">
                            <div class="section-preview">
                                <div class="preview-testimonials"></div>
                            </div>
                            <span>Testimonials</span>
                        </div>
                    </div>
                </div>

                <div class="section-category">
                    <h3>Business Sections</h3>
                    <div class="section-grid">
                        <div class="section-item" data-section="pricing">
                            <div class="section-preview">
                                <div class="preview-pricing"></div>
                            </div>
                            <span>Pricing</span>
                        </div>
                        <div class="section-item" data-section="team">
                            <div class="section-preview">
                                <div class="preview-team"></div>
                            </div>
                            <span>Team</span>
                        </div>
                        <div class="section-item" data-section="contact">
                            <div class="section-preview">
                                <div class="preview-contact"></div>
                            </div>
                            <span>Contact</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Templates Tab -->
            <div class="tab-content" id="templates-tab">
                <div class="template-grid">
                    <div class="template-item" data-template="business">
                        <div class="template-preview">
                            <img src="https://via.placeholder.com/280x120/2c3e50/ffffff?text=Business" alt="Business Template">
                        </div>
                        <h4>Corporate Business</h4>
                        <p>Professional business landing page</p>
                        <button class="btn btn-sm btn-primary">Use Template</button>
                    </div>
                    <div class="template-item" data-template="portfolio">
                        <div class="template-preview">
                            <img src="https://via.placeholder.com/280x120/e74c3c/ffffff?text=Portfolio" alt="Portfolio Template">
                        </div>
                        <h4>Creative Portfolio</h4>
                        <p>Showcase your creative work</p>
                        <button class="btn btn-sm btn-primary">Use Template</button>
                    </div>
                    <div class="template-item" data-template="startup">
                        <div class="template-preview">
                            <img src="https://via.placeholder.com/280x120/9b59b6/ffffff?text=Startup" alt="Startup Template">
                        </div>
                        <h4>Startup Landing</h4>
                        <p>Modern startup presentation</p>
                        <button class="btn btn-sm btn-primary">Use Template</button>
                    </div>
                    <div class="template-item" data-template="restaurant">
                        <div class="template-preview">
                            <img src="https://via.placeholder.com/280x120/f39c12/ffffff?text=Restaurant" alt="Restaurant Template">
                        </div>
                        <h4>Restaurant</h4>
                        <p>Delicious food experience</p>
                        <button class="btn btn-sm btn-primary">Use Template</button>
                    </div>
                    <div class="template-item" data-template="agency">
                        <div class="template-preview">
                            <img src="https://via.placeholder.com/280x120/1abc9c/ffffff?text=Agency" alt="Agency Template">
                        </div>
                        <h4>Digital Agency</h4>
                        <p>Creative solutions provider</p>
                        <button class="btn btn-sm btn-primary">Use Template</button>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-content" id="settings-tab">
                <div class="settings-group">
                    <h3>Page Settings</h3>
                    <div class="form-group">
                        <label>Page Title</label>
                        <input type="text" id="pageTitle" placeholder="Enter page title">
                    </div>
                    <div class="form-group">
                        <label>Meta Description</label>
                        <textarea id="metaDescription" placeholder="Enter meta description"></textarea>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>Global Styles</h3>
                    <div class="form-group">
                        <label>Primary Color</label>
                        <input type="color" id="primaryColor" value="#007bff">
                    </div>
                    <div class="form-group">
                        <label>Secondary Color</label>
                        <input type="color" id="secondaryColor" value="#6c757d">
                    </div>
                    <div class="form-group">
                        <label>Font Family</label>
                        <select id="fontFamily">
                            <option value="Inter">Inter</option>
                            <option value="Roboto">Roboto</option>
                            <option value="Open Sans">Open Sans</option>
                            <option value="Lato">Lato</option>
                        </select>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Canvas -->
        <main class="builder-canvas">
            <div class="canvas-container" id="canvas">
                <div class="empty-canvas">
                    <div class="empty-state">
                        <i class="fas fa-plus-circle"></i>
                        <h3>Start Building Your Page</h3>
                        <p>Drag sections from the sidebar to start building your page</p>
                    </div>
                </div>
            </div>
        </main>

        <!-- Properties Panel -->
        <aside class="properties-panel" id="propertiesPanel">
            <div class="panel-header">
                <h3>Properties</h3>
                <button class="close-panel" id="closePanelBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="panel-content" id="panelContent">
                <p>Select a section to edit its properties</p>
            </div>
        </aside>
    </div>

    <!-- Preview Modal -->
    <div class="modal" id="previewModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Page Preview</h3>
                <button class="close-modal" id="closePreviewBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <iframe id="previewFrame" src="about:blank"></iframe>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/builder.js"></script>
    <script src="assets/js/sections.js"></script>
    <script src="assets/js/templates.js"></script>
</body>
</html>
