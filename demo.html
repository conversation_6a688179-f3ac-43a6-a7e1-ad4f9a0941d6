<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LeadGen Builder - Demo Templates</title>
    <link rel="stylesheet" href="assets/css/sections.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .demo-header {
            background: #fff;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        
        .demo-header .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .demo-logo {
            font-size: 24px;
            font-weight: 700;
            color: #007bff;
        }
        
        .demo-nav {
            display: flex;
            gap: 30px;
        }
        
        .demo-nav a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .demo-nav a:hover {
            color: #007bff;
        }
        
        .builder-btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.3s;
        }
        
        .builder-btn:hover {
            background: #0056b3;
        }
        
        .demo-content {
            margin-top: 80px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            padding: 40px 0;
        }
        
        .demo-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
        }
        
        .demo-preview {
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #6c757d;
        }
        
        .demo-info {
            padding: 20px;
        }
        
        .demo-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .demo-info p {
            color: #6c757d;
            margin: 0 0 15px 0;
            line-height: 1.6;
        }
        
        .demo-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-preview {
            flex: 1;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            text-decoration: none;
            text-align: center;
            color: #333;
            transition: all 0.3s;
        }
        
        .btn-preview:hover {
            background: #e9ecef;
        }
        
        .btn-use {
            flex: 1;
            padding: 10px;
            background: #007bff;
            border: none;
            border-radius: 5px;
            color: white;
            text-decoration: none;
            text-align: center;
            transition: background 0.3s;
        }
        
        .btn-use:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <header class="demo-header">
        <div class="container">
            <div class="demo-logo">LeadGen Builder</div>
            <nav class="demo-nav">
                <a href="#templates">Templates</a>
                <a href="#features">Features</a>
                <a href="#pricing">Pricing</a>
                <a href="#support">Support</a>
            </nav>
            <a href="index.html" class="builder-btn">Open Builder</a>
        </div>
    </header>

    <main class="demo-content">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h1>Professional Templates for Every Business</h1>
                        <p>Choose from our collection of professionally designed templates and customize them with our drag-and-drop builder</p>
                        <a href="index.html" class="btn btn-primary">Start Building</a>
                        <a href="#templates" class="btn btn-secondary">Browse Templates</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Templates Section -->
        <section id="templates" class="section" style="background: #f8f9fa;">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2 style="text-align: center; margin-bottom: 50px;">Choose Your Template</h2>
                    </div>
                </div>
                
                <div class="demo-grid">
                    <div class="demo-card">
                        <div class="demo-preview" style="background: linear-gradient(135deg, #2c3e50, #3498db);">
                            <span style="color: white;">Corporate Business</span>
                        </div>
                        <div class="demo-info">
                            <h3>Corporate Business</h3>
                            <p>Professional business template perfect for corporate websites, consulting firms, and service providers.</p>
                            <div class="demo-actions">
                                <a href="templates/business.html" class="btn-preview">Preview</a>
                                <a href="index.html?template=business" class="btn-use">Use Template</a>
                            </div>
                        </div>
                    </div>

                    <div class="demo-card">
                        <div class="demo-preview" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                            <span style="color: white;">Creative Portfolio</span>
                        </div>
                        <div class="demo-info">
                            <h3>Creative Portfolio</h3>
                            <p>Showcase your creative work with this stunning portfolio template designed for artists and designers.</p>
                            <div class="demo-actions">
                                <a href="templates/portfolio.html" class="btn-preview">Preview</a>
                                <a href="index.html?template=portfolio" class="btn-use">Use Template</a>
                            </div>
                        </div>
                    </div>

                    <div class="demo-card">
                        <div class="demo-preview" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                            <span style="color: white;">Startup Landing</span>
                        </div>
                        <div class="demo-info">
                            <h3>Startup Landing</h3>
                            <p>Modern startup template perfect for launching new products and attracting investors.</p>
                            <div class="demo-actions">
                                <a href="templates/startup.html" class="btn-preview">Preview</a>
                                <a href="index.html?template=startup" class="btn-use">Use Template</a>
                            </div>
                        </div>
                    </div>

                    <div class="demo-card">
                        <div class="demo-preview" style="background: linear-gradient(135deg, #f39c12, #d35400);">
                            <span style="color: white;">Restaurant</span>
                        </div>
                        <div class="demo-info">
                            <h3>Restaurant</h3>
                            <p>Delicious template for restaurants, cafes, and food businesses to showcase their menu and atmosphere.</p>
                            <div class="demo-actions">
                                <a href="templates/restaurant.html" class="btn-preview">Preview</a>
                                <a href="index.html?template=restaurant" class="btn-use">Use Template</a>
                            </div>
                        </div>
                    </div>

                    <div class="demo-card">
                        <div class="demo-preview" style="background: linear-gradient(135deg, #1abc9c, #16a085);">
                            <span style="color: white;">Digital Agency</span>
                        </div>
                        <div class="demo-info">
                            <h3>Digital Agency</h3>
                            <p>Professional template for digital agencies, marketing firms, and creative service providers.</p>
                            <div class="demo-actions">
                                <a href="templates/agency.html" class="btn-preview">Preview</a>
                                <a href="index.html?template=agency" class="btn-use">Use Template</a>
                            </div>
                        </div>
                    </div>

                    <div class="demo-card">
                        <div class="demo-preview" style="background: linear-gradient(135deg, #34495e, #2c3e50);">
                            <span style="color: white;">Coming Soon</span>
                        </div>
                        <div class="demo-info">
                            <h3>More Templates</h3>
                            <p>We're constantly adding new templates. Check back soon for more amazing designs!</p>
                            <div class="demo-actions">
                                <a href="#" class="btn-preview" style="opacity: 0.5;">Coming Soon</a>
                                <a href="index.html" class="btn-use">Create Custom</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="features-section">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2>Powerful Features</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-4">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-mouse-pointer"></i>
                            </div>
                            <h3>Drag & Drop Builder</h3>
                            <p>Intuitive drag-and-drop interface makes it easy to build professional pages without coding.</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <h3>Responsive Design</h3>
                            <p>All templates are fully responsive and look great on desktop, tablet, and mobile devices.</p>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <h3>Full Customization</h3>
                            <p>Customize colors, fonts, images, and content to match your brand perfectly.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="contact-section">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <h2>Ready to Get Started?</h2>
                        <p>Create your professional landing page in minutes with our powerful drag-and-drop builder.</p>
                        <a href="index.html" class="btn btn-primary" style="font-size: 1.2rem; padding: 15px 30px;">Start Building Now</a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // Handle template loading from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const template = urlParams.get('template');
        
        if (template) {
            // Redirect to builder with template parameter
            window.location.href = `index.html?template=${template}`;
        }
    </script>
</body>
</html>
