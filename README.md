# LeadGen Builder - Multipurpose Site Builder

A powerful drag-and-drop website builder inspired by the LeadGen theme, allowing users to create professional landing pages and websites without coding knowledge.

## Features

### 🎨 Drag & Drop Builder
- Intuitive drag-and-drop interface
- Real-time editing and preview
- Section-based building approach
- Visual feedback during editing

### 📱 Responsive Design
- Desktop, tablet, and mobile preview modes
- Fully responsive sections and templates
- Mobile-first approach

### 🎯 Pre-built Sections
- **Hero Sections**: Banner and split layouts
- **Content Sections**: Features, about us, testimonials
- **Business Sections**: Pricing, team, contact forms
- All sections are fully customizable

### 🎨 Customization Options
- Color customization (primary, secondary, backgrounds)
- Typography settings (font family selection)
- Spacing controls (padding, margins)
- Content editing (text, images, buttons)
- Section-specific properties

### 📋 Professional Templates
- **Corporate Business**: Professional business landing page
- **Creative Portfolio**: Showcase creative work
- **Startup Landing**: Modern startup presentation
- **Restaurant**: Food business template
- **Digital Agency**: Creative services template

### 💾 Export & Import
- Export pages as HTML files
- Import/export page configurations as JSON
- Publish functionality for final HTML output
- Save and load project states

## Getting Started

### Quick Start
1. Open `index.html` in your web browser
2. Start building by dragging sections from the sidebar
3. Customize sections using the properties panel
4. Preview your page in different device modes
5. Export or publish when ready

### Using Templates
1. Visit `demo.html` to browse available templates
2. Click "Use Template" to load a template in the builder
3. Customize the template to match your needs
4. Export the final result

## File Structure

```
leadgen/
├── index.html              # Main builder interface
├── demo.html              # Template showcase page
├── README.md              # This file
└── assets/
    ├── css/
    │   ├── builder.css     # Builder interface styles
    │   └── sections.css    # Section and template styles
    └── js/
        ├── builder.js      # Main builder functionality
        ├── sections.js     # Section templates
        └── templates.js    # Page templates
```

## How to Use

### Building a Page
1. **Add Sections**: Drag sections from the sidebar to the canvas
2. **Edit Content**: Click on a section to select it and edit in the properties panel
3. **Customize Styling**: Use the properties panel to change colors, spacing, and content
4. **Preview**: Use the device preview buttons to see how your page looks on different screens
5. **Export**: Click "Export" to download your page configuration or "Publish" to get the HTML file

### Section Types

#### Hero Sections
- **Hero Banner**: Full-width hero with centered content
- **Hero Split**: Hero with content on one side and image on the other

#### Content Sections
- **Features 3 Col**: Three-column feature showcase
- **About Us**: About section with text and image
- **Testimonials**: Customer testimonials with avatars

#### Business Sections
- **Pricing**: Pricing tables with multiple plans
- **Team**: Team member showcase
- **Contact**: Contact form with contact information

### Customization Options

#### Global Settings
- Page title and meta description
- Primary and secondary colors
- Font family selection

#### Section Properties
- Background colors
- Padding and spacing
- Text content and styling
- Button colors and text
- Section-specific options

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Technical Details

### Technologies Used
- HTML5
- CSS3 (Flexbox, Grid, Custom Properties)
- Vanilla JavaScript (ES6+)
- Font Awesome icons
- Google Fonts

### Key Features Implementation
- **Drag & Drop**: HTML5 Drag and Drop API
- **Responsive Design**: CSS Grid and Flexbox
- **Color Customization**: CSS Custom Properties
- **Export/Import**: File API and Blob objects
- **Preview**: iframe-based preview system

## Customization and Extension

### Adding New Sections
1. Add section template to `assets/js/sections.js`
2. Add section preview to the sidebar in `index.html`
3. Add section-specific properties in `builder.js`
4. Style the section in `assets/css/sections.css`

### Adding New Templates
1. Define template structure in `assets/js/templates.js`
2. Add template preview to `demo.html`
3. Include template in the templates tab

### Styling Customization
- Modify `assets/css/builder.css` for builder interface
- Modify `assets/css/sections.css` for section styling
- Use CSS custom properties for easy theming

## License

This project is open source and available under the MIT License.

## Support

For support and questions, please refer to the documentation or create an issue in the project repository.

---

**LeadGen Builder** - Create professional landing pages with ease!
