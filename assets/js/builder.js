class PageBuilder {
    constructor() {
        this.canvas = document.getElementById('canvas');
        this.propertiesPanel = document.getElementById('propertiesPanel');
        this.selectedSection = null;
        this.sections = [];
        this.currentMode = 'desktop';
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.setupTabs();
        this.setupPreviewModes();
    }

    setupEventListeners() {
        // Header buttons
        document.getElementById('exportBtn').addEventListener('click', () => this.exportPage());
        document.getElementById('importBtn').addEventListener('click', () => this.importPage());
        document.getElementById('previewBtn').addEventListener('click', () => this.previewPage());
        document.getElementById('publishBtn').addEventListener('click', () => this.publishPage());
        
        // Properties panel
        document.getElementById('closePanelBtn').addEventListener('click', () => this.closePropertiesPanel());
        
        // Preview modal
        document.getElementById('closePreviewBtn').addEventListener('click', () => this.closePreview());
        
        // Global settings
        document.getElementById('primaryColor').addEventListener('change', (e) => this.updateGlobalStyle('--primary-color', e.target.value));
        document.getElementById('secondaryColor').addEventListener('change', (e) => this.updateGlobalStyle('--secondary-color', e.target.value));
        document.getElementById('fontFamily').addEventListener('change', (e) => this.updateGlobalStyle('--font-family', e.target.value));
    }

    setupTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.dataset.tab;
                
                // Remove active class from all tabs
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab
                btn.classList.add('active');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });
    }

    setupPreviewModes() {
        const previewBtns = document.querySelectorAll('.preview-btn');
        const canvasContainer = document.querySelector('.canvas-container');

        previewBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const mode = btn.dataset.mode;
                
                // Remove active class from all buttons
                previewBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // Update canvas mode
                canvasContainer.className = `canvas-container ${mode}-mode`;
                this.currentMode = mode;
            });
        });
    }

    setupDragAndDrop() {
        const sectionItems = document.querySelectorAll('.section-item');
        
        sectionItems.forEach(item => {
            item.draggable = true;
            
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', item.dataset.section);
                item.classList.add('section-dragging');
            });
            
            item.addEventListener('dragend', () => {
                item.classList.remove('section-dragging');
            });
        });

        // Canvas drop zone
        this.canvas.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.canvas.classList.add('drag-over');
        });

        this.canvas.addEventListener('dragleave', () => {
            this.canvas.classList.remove('drag-over');
        });

        this.canvas.addEventListener('drop', (e) => {
            e.preventDefault();
            this.canvas.classList.remove('drag-over');
            
            const sectionType = e.dataTransfer.getData('text/plain');
            this.addSection(sectionType);
        });
    }

    addSection(sectionType) {
        const sectionHtml = window.sectionTemplates.getSectionHtml(sectionType);
        const sectionElement = this.createSectionElement(sectionHtml, sectionType);

        // Remove empty state if it exists
        const emptyCanvas = this.canvas.querySelector('.empty-canvas');
        if (emptyCanvas) {
            emptyCanvas.remove();
        }

        this.canvas.appendChild(sectionElement);
        this.sections.push({
            id: sectionElement.id,
            type: sectionType,
            element: sectionElement
        });

        this.setupSectionEvents(sectionElement);
    }

    createSectionElement(html, type) {
        const wrapper = document.createElement('div');
        wrapper.className = 'section';
        wrapper.id = `section-${Date.now()}`;
        wrapper.dataset.sectionType = type;
        wrapper.innerHTML = html + this.getSectionControls();
        
        return wrapper;
    }

    getSectionControls() {
        return `
            <div class="section-controls">
                <button class="section-control-btn edit" title="Edit Section">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="section-control-btn duplicate" title="Duplicate Section">
                    <i class="fas fa-copy"></i>
                </button>
                <button class="section-control-btn delete" title="Delete Section">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }

    setupSectionEvents(sectionElement) {
        // Section selection
        sectionElement.addEventListener('click', (e) => {
            if (!e.target.closest('.section-controls')) {
                this.selectSection(sectionElement);
            }
        });

        // Section controls
        const editBtn = sectionElement.querySelector('.edit');
        const duplicateBtn = sectionElement.querySelector('.duplicate');
        const deleteBtn = sectionElement.querySelector('.delete');

        editBtn.addEventListener('click', () => this.editSection(sectionElement));
        duplicateBtn.addEventListener('click', () => this.duplicateSection(sectionElement));
        deleteBtn.addEventListener('click', () => this.deleteSection(sectionElement));
    }

    selectSection(sectionElement) {
        // Remove previous selection
        document.querySelectorAll('.section.selected').forEach(s => s.classList.remove('selected'));
        
        // Select new section
        sectionElement.classList.add('selected');
        this.selectedSection = sectionElement;
        
        // Show properties panel
        this.showPropertiesPanel(sectionElement);
    }

    editSection(sectionElement) {
        this.selectSection(sectionElement);
    }

    duplicateSection(sectionElement) {
        const clone = sectionElement.cloneNode(true);
        clone.id = `section-${Date.now()}`;
        sectionElement.parentNode.insertBefore(clone, sectionElement.nextSibling);
        
        this.setupSectionEvents(clone);
        this.sections.push({
            id: clone.id,
            type: sectionElement.dataset.sectionType,
            element: clone
        });
    }

    deleteSection(sectionElement) {
        if (confirm('Are you sure you want to delete this section?')) {
            const index = this.sections.findIndex(s => s.id === sectionElement.id);
            if (index > -1) {
                this.sections.splice(index, 1);
            }
            
            sectionElement.remove();
            this.closePropertiesPanel();
            
            // Show empty state if no sections
            if (this.sections.length === 0) {
                this.showEmptyState();
            }
        }
    }

    showEmptyState() {
        this.canvas.innerHTML = `
            <div class="empty-canvas">
                <div class="empty-state">
                    <i class="fas fa-plus-circle"></i>
                    <h3>Start Building Your Page</h3>
                    <p>Drag sections from the sidebar to start building your page</p>
                </div>
            </div>
        `;
    }

    showPropertiesPanel(sectionElement) {
        const sectionType = sectionElement.dataset.sectionType;
        const panelContent = document.getElementById('panelContent');
        
        panelContent.innerHTML = this.getPropertiesPanelContent(sectionType, sectionElement);
        this.propertiesPanel.classList.add('active');
        
        // Setup property change listeners
        this.setupPropertyListeners(sectionElement);
    }

    closePropertiesPanel() {
        this.propertiesPanel.classList.remove('active');
        document.querySelectorAll('.section.selected').forEach(s => s.classList.remove('selected'));
        this.selectedSection = null;
    }

    getPropertiesPanelContent(sectionType, sectionElement) {
        const baseProperties = `
            <div class="property-group">
                <h4>Section Settings</h4>
                <div class="form-group">
                    <label>Background Color</label>
                    <input type="color" id="sectionBgColor" value="#ffffff">
                </div>
                <div class="form-group">
                    <label>Padding Top (px)</label>
                    <input type="range" id="paddingTop" min="0" max="200" value="60">
                    <span class="range-value">60px</span>
                </div>
                <div class="form-group">
                    <label>Padding Bottom (px)</label>
                    <input type="range" id="paddingBottom" min="0" max="200" value="60">
                    <span class="range-value">60px</span>
                </div>
            </div>
        `;

        const contentProperties = this.getContentProperties(sectionType, sectionElement);
        const specificProperties = this.getSpecificProperties(sectionType, sectionElement);

        return baseProperties + contentProperties + specificProperties;
    }

    getContentProperties(sectionType, sectionElement) {
        if (sectionType.includes('hero') || sectionType === 'about-us' || sectionType === 'contact') {
            return `
                <div class="property-group">
                    <h4>Content</h4>
                    <div class="form-group">
                        <label>Heading Text</label>
                        <input type="text" id="headingText" placeholder="Enter heading">
                    </div>
                    <div class="form-group">
                        <label>Description Text</label>
                        <textarea id="descriptionText" placeholder="Enter description"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Button Text</label>
                        <input type="text" id="buttonText" placeholder="Enter button text">
                    </div>
                    <div class="form-group">
                        <label>Button Color</label>
                        <input type="color" id="buttonColor" value="#007bff">
                    </div>
                </div>
            `;
        }

        if (sectionType === 'features-3col') {
            return `
                <div class="property-group">
                    <h4>Features Content</h4>
                    <div class="form-group">
                        <label>Section Title</label>
                        <input type="text" id="sectionTitle" placeholder="Enter section title">
                    </div>
                    <div class="form-group">
                        <label>Feature 1 Title</label>
                        <input type="text" id="feature1Title" placeholder="Feature 1 title">
                    </div>
                    <div class="form-group">
                        <label>Feature 1 Description</label>
                        <textarea id="feature1Desc" placeholder="Feature 1 description"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Feature 2 Title</label>
                        <input type="text" id="feature2Title" placeholder="Feature 2 title">
                    </div>
                    <div class="form-group">
                        <label>Feature 2 Description</label>
                        <textarea id="feature2Desc" placeholder="Feature 2 description"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Feature 3 Title</label>
                        <input type="text" id="feature3Title" placeholder="Feature 3 title">
                    </div>
                    <div class="form-group">
                        <label>Feature 3 Description</label>
                        <textarea id="feature3Desc" placeholder="Feature 3 description"></textarea>
                    </div>
                </div>
            `;
        }

        return `
            <div class="property-group">
                <h4>Content</h4>
                <div class="form-group">
                    <label>Section Title</label>
                    <input type="text" id="sectionTitle" placeholder="Enter section title">
                </div>
            </div>
        `;
    }

    getSpecificProperties(sectionType, sectionElement) {
        if (sectionType.includes('hero')) {
            return `
                <div class="property-group">
                    <h4>Hero Settings</h4>
                    <div class="form-group">
                        <label>Background Type</label>
                        <select id="heroBackground">
                            <option value="gradient">Gradient</option>
                            <option value="image">Image</option>
                            <option value="color">Solid Color</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Text Color</label>
                        <input type="color" id="textColor" value="#ffffff">
                    </div>
                </div>
            `;
        }

        if (sectionType === 'pricing') {
            return `
                <div class="property-group">
                    <h4>Pricing Settings</h4>
                    <div class="form-group">
                        <label>Currency Symbol</label>
                        <input type="text" id="currencySymbol" value="$" maxlength="3">
                    </div>
                    <div class="form-group">
                        <label>Billing Period</label>
                        <select id="billingPeriod">
                            <option value="month">Monthly</option>
                            <option value="year">Yearly</option>
                            <option value="week">Weekly</option>
                        </select>
                    </div>
                </div>
            `;
        }

        return '';
    }

    setupPropertyListeners(sectionElement) {
        // Base properties
        this.setupBasePropertyListeners(sectionElement);

        // Content properties
        this.setupContentPropertyListeners(sectionElement);

        // Specific properties
        this.setupSpecificPropertyListeners(sectionElement);
    }

    setupBasePropertyListeners(sectionElement) {
        const bgColorInput = document.getElementById('sectionBgColor');
        const paddingTopInput = document.getElementById('paddingTop');
        const paddingBottomInput = document.getElementById('paddingBottom');

        if (bgColorInput) {
            // Set current background color
            const currentBg = getComputedStyle(sectionElement).backgroundColor;
            if (currentBg !== 'rgba(0, 0, 0, 0)') {
                bgColorInput.value = this.rgbToHex(currentBg);
            }

            bgColorInput.addEventListener('change', (e) => {
                sectionElement.style.backgroundColor = e.target.value;
            });
        }

        if (paddingTopInput) {
            const rangeValue = paddingTopInput.nextElementSibling;
            paddingTopInput.addEventListener('input', (e) => {
                const value = e.target.value;
                sectionElement.style.paddingTop = value + 'px';
                if (rangeValue) rangeValue.textContent = value + 'px';
            });
        }

        if (paddingBottomInput) {
            const rangeValue = paddingBottomInput.nextElementSibling;
            paddingBottomInput.addEventListener('input', (e) => {
                const value = e.target.value;
                sectionElement.style.paddingBottom = value + 'px';
                if (rangeValue) rangeValue.textContent = value + 'px';
            });
        }
    }

    setupContentPropertyListeners(sectionElement) {
        const headingInput = document.getElementById('headingText');
        const descriptionInput = document.getElementById('descriptionText');
        const buttonInput = document.getElementById('buttonText');
        const buttonColorInput = document.getElementById('buttonColor');
        const sectionTitleInput = document.getElementById('sectionTitle');

        if (headingInput) {
            const heading = sectionElement.querySelector('h1, h2, h3');
            if (heading) {
                headingInput.value = heading.textContent;
                headingInput.addEventListener('input', (e) => {
                    heading.textContent = e.target.value;
                });
            }
        }

        if (descriptionInput) {
            const description = sectionElement.querySelector('p');
            if (description) {
                descriptionInput.value = description.textContent;
                descriptionInput.addEventListener('input', (e) => {
                    description.textContent = e.target.value;
                });
            }
        }

        if (buttonInput) {
            const button = sectionElement.querySelector('.btn');
            if (button) {
                buttonInput.value = button.textContent;
                buttonInput.addEventListener('input', (e) => {
                    button.textContent = e.target.value;
                });
            }
        }

        if (buttonColorInput) {
            const button = sectionElement.querySelector('.btn');
            if (button) {
                buttonColorInput.addEventListener('change', (e) => {
                    button.style.backgroundColor = e.target.value;
                });
            }
        }

        if (sectionTitleInput) {
            const title = sectionElement.querySelector('h2');
            if (title) {
                sectionTitleInput.value = title.textContent;
                sectionTitleInput.addEventListener('input', (e) => {
                    title.textContent = e.target.value;
                });
            }
        }

        // Features specific listeners
        this.setupFeatureListeners(sectionElement);
    }

    setupFeatureListeners(sectionElement) {
        for (let i = 1; i <= 3; i++) {
            const titleInput = document.getElementById(`feature${i}Title`);
            const descInput = document.getElementById(`feature${i}Desc`);

            if (titleInput) {
                const featureTitle = sectionElement.querySelector(`.feature-item:nth-child(${i}) h3`);
                if (featureTitle) {
                    titleInput.value = featureTitle.textContent;
                    titleInput.addEventListener('input', (e) => {
                        featureTitle.textContent = e.target.value;
                    });
                }
            }

            if (descInput) {
                const featureDesc = sectionElement.querySelector(`.feature-item:nth-child(${i}) p`);
                if (featureDesc) {
                    descInput.value = featureDesc.textContent;
                    descInput.addEventListener('input', (e) => {
                        featureDesc.textContent = e.target.value;
                    });
                }
            }
        }
    }

    setupSpecificPropertyListeners(sectionElement) {
        const heroBackgroundSelect = document.getElementById('heroBackground');
        const textColorInput = document.getElementById('textColor');
        const currencyInput = document.getElementById('currencySymbol');
        const billingPeriodSelect = document.getElementById('billingPeriod');

        if (heroBackgroundSelect) {
            heroBackgroundSelect.addEventListener('change', (e) => {
                const value = e.target.value;
                if (value === 'gradient') {
                    sectionElement.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                } else if (value === 'color') {
                    sectionElement.style.background = '#007bff';
                } else if (value === 'image') {
                    sectionElement.style.background = 'url(https://via.placeholder.com/1200x600/007bff/ffffff?text=Hero+Background) center/cover';
                }
            });
        }

        if (textColorInput) {
            textColorInput.addEventListener('change', (e) => {
                sectionElement.style.color = e.target.value;
            });
        }

        if (currencyInput) {
            currencyInput.addEventListener('input', (e) => {
                const prices = sectionElement.querySelectorAll('.pricing-price');
                prices.forEach(price => {
                    const priceText = price.textContent;
                    const number = priceText.match(/\d+/);
                    if (number) {
                        price.innerHTML = `${e.target.value}${number[0]}<span>/month</span>`;
                    }
                });
            });
        }

        if (billingPeriodSelect) {
            billingPeriodSelect.addEventListener('change', (e) => {
                const spans = sectionElement.querySelectorAll('.pricing-price span');
                spans.forEach(span => {
                    span.textContent = `/${e.target.value}`;
                });
            });
        }
    }

    rgbToHex(rgb) {
        const result = rgb.match(/\d+/g);
        if (result) {
            return "#" + ((1 << 24) + (parseInt(result[0]) << 16) + (parseInt(result[1]) << 8) + parseInt(result[2])).toString(16).slice(1);
        }
        return '#ffffff';
    }

    updateGlobalStyle(property, value) {
        document.documentElement.style.setProperty(property, value);
    }

    exportPage() {
        const pageData = {
            sections: this.sections.map(section => ({
                id: section.id,
                type: section.type,
                html: section.element.innerHTML,
                styles: section.element.style.cssText
            })),
            settings: {
                title: document.getElementById('pageTitle').value,
                description: document.getElementById('metaDescription').value,
                primaryColor: document.getElementById('primaryColor').value,
                secondaryColor: document.getElementById('secondaryColor').value,
                fontFamily: document.getElementById('fontFamily').value
            }
        };

        const dataStr = JSON.stringify(pageData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = 'page-data.json';
        link.click();
        
        URL.revokeObjectURL(url);
    }

    importPage() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const pageData = JSON.parse(e.target.result);
                        this.loadPageData(pageData);
                    } catch (error) {
                        alert('Error importing page data: ' + error.message);
                    }
                };
                reader.readAsText(file);
            }
        });
        
        input.click();
    }

    loadPageData(pageData) {
        // Clear current page
        this.canvas.innerHTML = '';
        this.sections = [];
        
        // Load sections
        pageData.sections.forEach(sectionData => {
            const sectionElement = document.createElement('div');
            sectionElement.className = 'section';
            sectionElement.id = sectionData.id;
            sectionElement.dataset.sectionType = sectionData.type;
            sectionElement.innerHTML = sectionData.html;
            sectionElement.style.cssText = sectionData.styles;
            
            this.canvas.appendChild(sectionElement);
            this.setupSectionEvents(sectionElement);
            
            this.sections.push({
                id: sectionData.id,
                type: sectionData.type,
                element: sectionElement
            });
        });
        
        // Load settings
        if (pageData.settings) {
            document.getElementById('pageTitle').value = pageData.settings.title || '';
            document.getElementById('metaDescription').value = pageData.settings.description || '';
            document.getElementById('primaryColor').value = pageData.settings.primaryColor || '#007bff';
            document.getElementById('secondaryColor').value = pageData.settings.secondaryColor || '#6c757d';
            document.getElementById('fontFamily').value = pageData.settings.fontFamily || 'Inter';
        }
    }

    previewPage() {
        const modal = document.getElementById('previewModal');
        const iframe = document.getElementById('previewFrame');
        
        const pageHtml = this.generatePreviewHtml();
        const blob = new Blob([pageHtml], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        
        iframe.src = url;
        modal.classList.add('active');
    }

    closePreview() {
        const modal = document.getElementById('previewModal');
        const iframe = document.getElementById('previewFrame');
        
        modal.classList.remove('active');
        URL.revokeObjectURL(iframe.src);
        iframe.src = 'about:blank';
    }

    generatePreviewHtml() {
        const sectionsHtml = Array.from(this.canvas.children)
            .filter(child => child.classList.contains('section'))
            .map(section => {
                const clone = section.cloneNode(true);
                // Remove builder-specific elements
                const controls = clone.querySelector('.section-controls');
                if (controls) controls.remove();
                clone.classList.remove('selected');
                return clone.outerHTML;
            })
            .join('');

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${document.getElementById('pageTitle').value || 'Generated Page'}</title>
                <meta name="description" content="${document.getElementById('metaDescription').value || ''}">
                <link rel="stylesheet" href="assets/css/sections.css">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
                <style>
                    :root {
                        --primary-color: ${document.getElementById('primaryColor').value};
                        --secondary-color: ${document.getElementById('secondaryColor').value};
                        --font-family: ${document.getElementById('fontFamily').value};
                    }
                    body { font-family: var(--font-family), sans-serif; margin: 0; }
                </style>
            </head>
            <body>
                ${sectionsHtml}
            </body>
            </html>
        `;
    }

    publishPage() {
        const html = this.generatePreviewHtml();
        const blob = new Blob([html], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = 'index.html';
        link.click();
        
        URL.revokeObjectURL(url);
        
        alert('Page published successfully! The HTML file has been downloaded.');
    }
}

// Initialize the page builder when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pageBuilder = new PageBuilder();

    // Check for template parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const template = urlParams.get('template');

    if (template && window.pageTemplates) {
        // Wait for templates to be initialized
        setTimeout(() => {
            window.pageTemplates.loadTemplate(template);
        }, 100);
    }
});
