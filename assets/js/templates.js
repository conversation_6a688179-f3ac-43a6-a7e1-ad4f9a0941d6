class PageTemplates {
    constructor() {
        this.templates = {
            'business': this.getBusinessTemplate(),
            'portfolio': this.getPortfolioTemplate(),
            'startup': this.getStartupTemplate(),
            'restaurant': this.getRestaurantTemplate(),
            'agency': this.getAgencyTemplate()
        };
        
        this.init();
    }

    init() {
        this.setupTemplateButtons();
    }

    setupTemplateButtons() {
        const templateItems = document.querySelectorAll('.template-item');
        
        templateItems.forEach(item => {
            const button = item.querySelector('.btn');
            if (button) {
                button.addEventListener('click', () => {
                    const templateType = item.dataset.template;
                    this.loadTemplate(templateType);
                });
            }
        });
    }

    loadTemplate(templateType) {
        const template = this.templates[templateType];
        if (!template) {
            alert('Template not found!');
            return;
        }

        if (confirm('This will replace your current page. Are you sure?')) {
            this.applyTemplate(template);
        }
    }

    applyTemplate(template) {
        // Clear current canvas
        const canvas = document.getElementById('canvas');
        canvas.innerHTML = '';
        window.pageBuilder.sections = [];

        // Apply template settings
        if (template.settings) {
            document.getElementById('pageTitle').value = template.settings.title || '';
            document.getElementById('metaDescription').value = template.settings.description || '';
            document.getElementById('primaryColor').value = template.settings.primaryColor || '#007bff';
            document.getElementById('secondaryColor').value = template.settings.secondaryColor || '#6c757d';
            document.getElementById('fontFamily').value = template.settings.fontFamily || 'Inter';
        }

        // Add template sections
        template.sections.forEach(sectionType => {
            window.pageBuilder.addSection(sectionType);
        });

        // Switch to sections tab
        document.querySelector('[data-tab="sections"]').click();
    }

    getBusinessTemplate() {
        return {
            settings: {
                title: 'Corporate Business - Professional Services',
                description: 'Professional business services to help your company grow and succeed in today\'s competitive market.',
                primaryColor: '#2c3e50',
                secondaryColor: '#3498db',
                fontFamily: 'Inter'
            },
            sections: ['hero-2', 'features-3col', 'about-us', 'testimonials', 'pricing', 'contact']
        };
    }

    getPortfolioTemplate() {
        return {
            settings: {
                title: 'Creative Portfolio - Showcase Your Work',
                description: 'A stunning portfolio to showcase your creative work and attract potential clients.',
                primaryColor: '#e74c3c',
                secondaryColor: '#34495e',
                fontFamily: 'Inter'
            },
            sections: ['hero-1', 'about-us', 'team', 'testimonials', 'contact']
        };
    }

    getStartupTemplate() {
        return {
            settings: {
                title: 'Startup Landing - Launch Your Idea',
                description: 'Modern startup landing page to present your innovative idea and attract investors.',
                primaryColor: '#9b59b6',
                secondaryColor: '#2ecc71',
                fontFamily: 'Inter'
            },
            sections: ['hero-1', 'features-3col', 'pricing', 'team', 'contact']
        };
    }

    getRestaurantTemplate() {
        return {
            settings: {
                title: 'Restaurant - Delicious Food Experience',
                description: 'Showcase your restaurant\'s delicious menu and create an amazing dining experience.',
                primaryColor: '#f39c12',
                secondaryColor: '#d35400',
                fontFamily: 'Inter'
            },
            sections: ['hero-2', 'about-us', 'features-3col', 'testimonials', 'contact']
        };
    }

    getAgencyTemplate() {
        return {
            settings: {
                title: 'Digital Agency - Creative Solutions',
                description: 'Full-service digital agency providing creative solutions for modern businesses.',
                primaryColor: '#1abc9c',
                secondaryColor: '#16a085',
                fontFamily: 'Inter'
            },
            sections: ['hero-1', 'features-3col', 'team', 'pricing', 'testimonials', 'contact']
        };
    }
}

// Initialize page templates when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.pageTemplates = new PageTemplates();
});
