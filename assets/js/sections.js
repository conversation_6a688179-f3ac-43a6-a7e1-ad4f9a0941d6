class SectionTemplates {
    constructor() {
        this.templates = {
            'hero-1': this.getHero1Template(),
            'hero-2': this.getHero2Template(),
            'features-3col': this.getFeatures3ColTemplate(),
            'about-us': this.getAboutUsTemplate(),
            'testimonials': this.getTestimonialsTemplate(),
            'pricing': this.getPricingTemplate(),
            'team': this.getTeamTemplate(),
            'contact': this.getContactTemplate()
        };
    }

    getSectionHtml(sectionType) {
        return this.templates[sectionType] || '<div>Section not found</div>';
    }

    getHero1Template() {
        return `
            <div class="hero-section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h1>Build Amazing Landing Pages</h1>
                            <p>Create stunning, conversion-focused landing pages with our drag-and-drop builder. No coding required!</p>
                            <button class="btn btn-primary">Get Started</button>
                            <button class="btn btn-secondary">Learn More</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getHero2Template() {
        return `
            <div class="hero-section">
                <div class="container">
                    <div class="hero-split">
                        <div class="hero-content">
                            <h1>Transform Your Business</h1>
                            <p>Powerful tools and features to help you grow your business and reach more customers online.</p>
                            <button class="btn btn-primary">Start Free Trial</button>
                        </div>
                        <div class="hero-image">
                            <img src="https://via.placeholder.com/500x400/007bff/ffffff?text=Hero+Image" alt="Hero Image">
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getFeatures3ColTemplate() {
        return `
            <div class="features-section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h2>Why Choose Our Platform</h2>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-4">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <h3>Fast & Reliable</h3>
                                <p>Lightning-fast performance with 99.9% uptime guarantee. Your website will always be available when your customers need it.</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h3>Secure & Safe</h3>
                                <p>Enterprise-grade security with SSL encryption, regular backups, and advanced threat protection for your peace of mind.</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-headset"></i>
                                </div>
                                <h3>24/7 Support</h3>
                                <p>Round-the-clock customer support from our expert team. Get help whenever you need it, day or night.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getAboutUsTemplate() {
        return `
            <div class="about-section">
                <div class="container">
                    <div class="about-content">
                        <div class="about-text">
                            <h2>About Our Company</h2>
                            <p>We are a team of passionate professionals dedicated to helping businesses succeed online. With over 10 years of experience in web development and digital marketing, we understand what it takes to create effective online presence.</p>
                            <p>Our mission is to provide powerful, easy-to-use tools that enable anyone to create professional websites and landing pages without technical expertise.</p>
                            <button class="btn btn-primary">Learn More</button>
                        </div>
                        <div class="about-image">
                            <img src="https://via.placeholder.com/500x400/28a745/ffffff?text=About+Us" alt="About Us">
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getTestimonialsTemplate() {
        return `
            <div class="testimonials-section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h2>What Our Customers Say</h2>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="testimonial-item">
                                <div class="testimonial-text">
                                    "This platform has completely transformed how we create landing pages. The drag-and-drop interface is intuitive and the results are professional."
                                </div>
                                <div class="testimonial-author">
                                    <div class="author-avatar">JD</div>
                                    <div class="author-info">
                                        <h4>John Doe</h4>
                                        <p>Marketing Director</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="testimonial-item">
                                <div class="testimonial-text">
                                    "Amazing tool! We've increased our conversion rates by 40% since switching to this platform. Highly recommended for any business."
                                </div>
                                <div class="testimonial-author">
                                    <div class="author-avatar">SM</div>
                                    <div class="author-info">
                                        <h4>Sarah Miller</h4>
                                        <p>CEO, TechStart</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getPricingTemplate() {
        return `
            <div class="pricing-section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h2>Choose Your Plan</h2>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-4">
                            <div class="pricing-card">
                                <div class="pricing-plan">Starter</div>
                                <div class="pricing-price">$19<span>/month</span></div>
                                <ul class="pricing-features">
                                    <li>5 Landing Pages</li>
                                    <li>Basic Templates</li>
                                    <li>Email Support</li>
                                    <li>SSL Certificate</li>
                                </ul>
                                <button class="btn btn-primary">Get Started</button>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="pricing-card featured">
                                <div class="pricing-plan">Professional</div>
                                <div class="pricing-price">$49<span>/month</span></div>
                                <ul class="pricing-features">
                                    <li>Unlimited Pages</li>
                                    <li>Premium Templates</li>
                                    <li>Priority Support</li>
                                    <li>Custom Domain</li>
                                    <li>Analytics Dashboard</li>
                                </ul>
                                <button class="btn btn-primary">Get Started</button>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="pricing-card">
                                <div class="pricing-plan">Enterprise</div>
                                <div class="pricing-price">$99<span>/month</span></div>
                                <ul class="pricing-features">
                                    <li>Everything in Pro</li>
                                    <li>White Label</li>
                                    <li>API Access</li>
                                    <li>Dedicated Support</li>
                                    <li>Custom Integrations</li>
                                </ul>
                                <button class="btn btn-primary">Contact Sales</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getTeamTemplate() {
        return `
            <div class="team-section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h2>Meet Our Team</h2>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-4">
                            <div class="team-member">
                                <div class="member-avatar">AB</div>
                                <div class="member-name">Alex Brown</div>
                                <div class="member-role">CEO & Founder</div>
                                <div class="member-bio">Passionate entrepreneur with 15+ years of experience in tech industry.</div>
                                <div class="member-social">
                                    <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                                    <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="team-member">
                                <div class="member-avatar">EJ</div>
                                <div class="member-name">Emily Johnson</div>
                                <div class="member-role">Lead Designer</div>
                                <div class="member-bio">Creative designer focused on user experience and beautiful interfaces.</div>
                                <div class="member-social">
                                    <a href="#" class="social-link"><i class="fab fa-dribbble"></i></a>
                                    <a href="#" class="social-link"><i class="fab fa-behance"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="team-member">
                                <div class="member-avatar">MW</div>
                                <div class="member-name">Mike Wilson</div>
                                <div class="member-role">Lead Developer</div>
                                <div class="member-bio">Full-stack developer with expertise in modern web technologies.</div>
                                <div class="member-social">
                                    <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                                    <a href="#" class="social-link"><i class="fab fa-stackoverflow"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getContactTemplate() {
        return `
            <div class="contact-section">
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <h2>Get In Touch</h2>
                            <p>Ready to get started? Contact us today and let's discuss how we can help your business grow.</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <form class="contact-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>First Name</label>
                                        <input type="text" name="firstName" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Last Name</label>
                                        <input type="text" name="lastName" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label>Email</label>
                                        <input type="email" name="email" required>
                                    </div>
                                    <div class="form-group">
                                        <label>Phone</label>
                                        <input type="tel" name="phone">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Message</label>
                                    <textarea name="message" required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">Send Message</button>
                            </form>
                        </div>
                    </div>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <h4>Phone</h4>
                            <p>+****************</p>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <h4>Address</h4>
                            <p>123 Business St, City, State 12345</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
}

// Initialize section templates
window.sectionTemplates = new SectionTemplates();
