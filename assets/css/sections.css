/* Section Base Styles */
.section {
    position: relative;
    padding: 60px 0;
    transition: all 0.3s;
}

.section:hover {
    outline: 2px solid #007bff;
    outline-offset: -2px;
}

.section.selected {
    outline: 2px solid #28a745;
    outline-offset: -2px;
}

.section-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: none;
    gap: 5px;
    z-index: 10;
}

.section:hover .section-controls,
.section.selected .section-controls {
    display: flex;
}

.section-control-btn {
    width: 30px;
    height: 30px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s;
}

.section-control-btn:hover {
    background: #0056b3;
}

.section-control-btn.delete {
    background: #dc3545;
}

.section-control-btn.delete:hover {
    background: #c82333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    padding: 0 15px;
}

.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }

/* Hero Sections */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 120px 0;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-section p {
    font-size: 1.25rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-section .btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    margin: 0 10px;
}

.hero-split {
    display: flex;
    align-items: center;
    min-height: 600px;
}

.hero-content {
    flex: 1;
    padding-right: 40px;
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
}

/* Features Section */
.features-section {
    background: #f8f9fa;
}

.features-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: #333;
}

.feature-item {
    text-align: center;
    padding: 30px 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    transition: transform 0.3s;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
    color: white;
}

.feature-item h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #333;
}

.feature-item p {
    color: #6c757d;
    line-height: 1.6;
}

/* About Section */
.about-section {
    padding: 100px 0;
}

.about-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.about-text {
    flex: 1;
}

.about-text h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: #333;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #6c757d;
    margin-bottom: 20px;
}

.about-image {
    flex: 1;
}

.about-image img {
    width: 100%;
    border-radius: 10px;
}

/* Testimonials Section */
.testimonials-section {
    background: #f8f9fa;
    text-align: center;
}

.testimonials-section h2 {
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: #333;
}

.testimonial-item {
    background: white;
    padding: 40px 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.testimonial-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
    margin-bottom: 30px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #007bff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.author-info h4 {
    color: #333;
    margin-bottom: 5px;
}

.author-info p {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Pricing Section */
.pricing-section {
    text-align: center;
}

.pricing-section h2 {
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: #333;
}

.pricing-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 40px 30px;
    margin-bottom: 30px;
    transition: all 0.3s;
    position: relative;
}

.pricing-card:hover {
    border-color: #007bff;
    transform: translateY(-5px);
}

.pricing-card.featured {
    border-color: #007bff;
    transform: scale(1.05);
}

.pricing-card.featured::before {
    content: 'Most Popular';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #007bff;
    color: white;
    padding: 5px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.pricing-plan {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.pricing-price {
    font-size: 3rem;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 20px;
}

.pricing-price span {
    font-size: 1rem;
    color: #6c757d;
}

.pricing-features {
    list-style: none;
    margin-bottom: 30px;
}

.pricing-features li {
    padding: 10px 0;
    border-bottom: 1px solid #f8f9fa;
    color: #333;
}

.pricing-features li:last-child {
    border-bottom: none;
}

/* Team Section */
.team-section {
    background: #f8f9fa;
    text-align: center;
}

.team-section h2 {
    font-size: 2.5rem;
    margin-bottom: 50px;
    color: #333;
}

.team-member {
    background: white;
    border-radius: 10px;
    padding: 30px 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    transition: transform 0.3s;
}

.team-member:hover {
    transform: translateY(-5px);
}

.member-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: #007bff;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
}

.member-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.member-role {
    color: #007bff;
    margin-bottom: 15px;
}

.member-bio {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 20px;
}

.member-social {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.3s;
}

.social-link:hover {
    background: #007bff;
    color: white;
}

/* Contact Section */
.contact-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.contact-section h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.contact-section p {
    font-size: 1.1rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
    text-align: left;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 40px;
}

.contact-item {
    text-align: center;
}

.contact-item i {
    font-size: 2rem;
    margin-bottom: 10px;
    opacity: 0.8;
}

.contact-item h4 {
    margin-bottom: 5px;
}

.contact-item p {
    opacity: 0.8;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-split {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-content {
        padding-right: 0;
        margin-bottom: 40px;
    }
    
    .about-content {
        flex-direction: column;
        text-align: center;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .contact-info {
        flex-direction: column;
        gap: 20px;
    }
    
    .col-6,
    .col-4,
    .col-3 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
