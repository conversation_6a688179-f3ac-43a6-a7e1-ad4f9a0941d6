/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #f8f9fa;
    overflow: hidden;
}

/* Header Styles */
.builder-header {
    height: 60px;
    background: #fff;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left .logo {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.header-left .logo i {
    margin-right: 8px;
    color: #007bff;
}

.header-center .preview-modes {
    display: flex;
    background: #f8f9fa;
    border-radius: 6px;
    padding: 4px;
}

.preview-btn {
    background: none;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.2s;
}

.preview-btn.active {
    background: #fff;
    color: #007bff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header-right {
    display: flex;
    gap: 10px;
}

/* Button Styles */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Main Container */
.builder-container {
    display: flex;
    height: calc(100vh - 60px);
    margin-top: 60px;
}

/* Sidebar Styles */
.builder-sidebar {
    width: 300px;
    background: #fff;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
}

.sidebar-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 15px 10px;
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    color: #6c757d;
    transition: all 0.2s;
}

.tab-btn.active {
    color: #007bff;
    background: #f8f9fa;
}

.tab-btn i {
    font-size: 18px;
}

.tab-btn span {
    font-size: 12px;
    font-weight: 500;
}

.tab-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: none;
}

.tab-content.active {
    display: block;
}

/* Section Categories */
.section-category {
    margin-bottom: 30px;
}

.section-category h3 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.section-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.section-item {
    background: #f8f9fa;
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 12px;
    cursor: grab;
    transition: all 0.2s;
    text-align: center;
}

.section-item:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.section-item:active {
    cursor: grabbing;
}

.section-preview {
    height: 60px;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.section-item span {
    font-size: 12px;
    font-weight: 500;
    color: #333;
}

/* Preview Elements */
.preview-hero {
    width: 80%;
    height: 20px;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 2px;
}

.preview-hero-split {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #007bff 50%, #f8f9fa 50%);
}

.preview-features {
    display: flex;
    gap: 4px;
    width: 80%;
}

.preview-features::before,
.preview-features::after {
    content: '';
    flex: 1;
    height: 30px;
    background: #007bff;
    border-radius: 2px;
}

.preview-features {
    position: relative;
}

.preview-features::before {
    background: #007bff;
}

.preview-features::after {
    background: #28a745;
}

.preview-about {
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        45deg,
        #007bff,
        #007bff 10px,
        #f8f9fa 10px,
        #f8f9fa 20px
    );
}

.preview-testimonials {
    width: 60%;
    height: 30px;
    background: #ffc107;
    border-radius: 15px;
    position: relative;
}

.preview-testimonials::before {
    content: '"';
    position: absolute;
    top: -5px;
    left: 5px;
    font-size: 20px;
    color: #fff;
}

.preview-pricing {
    display: flex;
    gap: 2px;
    width: 80%;
    height: 40px;
}

.preview-pricing::before,
.preview-pricing::after {
    content: '';
    flex: 1;
    background: #007bff;
    border-radius: 2px;
}

.preview-pricing::after {
    background: #28a745;
}

.preview-team {
    display: flex;
    gap: 4px;
    width: 70%;
}

.preview-team::before,
.preview-team::after {
    content: '';
    width: 20px;
    height: 20px;
    background: #6c757d;
    border-radius: 50%;
}

.preview-contact {
    width: 80%;
    height: 30px;
    background: #17a2b8;
    border-radius: 4px;
    position: relative;
}

.preview-contact::before {
    content: '✉';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 14px;
}

/* Template Grid */
.template-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.template-item {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s;
}

.template-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.template-preview {
    height: 120px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-item h4 {
    padding: 12px 15px 5px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.template-item p {
    padding: 0 15px;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 15px;
}

.template-item .btn {
    margin: 0 15px 15px;
    width: calc(100% - 30px);
    justify-content: center;
}

/* Settings Form */
.settings-group {
    margin-bottom: 30px;
}

.settings-group h3 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
}

/* Canvas Styles */
.builder-canvas {
    flex: 1;
    background: #f8f9fa;
    position: relative;
    overflow: auto;
}

.canvas-container {
    min-height: 100%;
    background: #fff;
    margin: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s;
}

.canvas-container.tablet-mode {
    max-width: 768px;
    margin: 20px auto;
}

.canvas-container.mobile-mode {
    max-width: 375px;
    margin: 20px auto;
}

.empty-canvas {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 500px;
}

.empty-state {
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    color: #dee2e6;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 10px;
}

.empty-state p {
    font-size: 14px;
}

/* Properties Panel */
.properties-panel {
    width: 300px;
    background: #fff;
    border-left: 1px solid #e9ecef;
    display: none;
    flex-direction: column;
}

.properties-panel.active {
    display: flex;
}

.panel-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.close-panel {
    background: none;
    border: none;
    cursor: pointer;
    color: #6c757d;
    font-size: 16px;
    padding: 4px;
}

.panel-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* Property Groups */
.property-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.property-group:last-child {
    border-bottom: none;
}

.property-group h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Range Input Styling */
.form-group {
    position: relative;
}

.range-value {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Color Input Styling */
input[type="color"] {
    width: 100%;
    height: 40px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    cursor: pointer;
    padding: 2px;
}

input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
}

input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: 2px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 1200px;
    height: 80%;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.close-modal {
    background: none;
    border: none;
    cursor: pointer;
    color: #6c757d;
    font-size: 18px;
    padding: 4px;
}

.modal-body {
    flex: 1;
    padding: 20px;
}

#previewFrame {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 4px;
}

/* Drag and Drop Styles */
.drag-over {
    background: #e3f2fd !important;
    border: 2px dashed #007bff !important;
}

.section-dragging {
    opacity: 0.5;
}

/* Responsive */
@media (max-width: 768px) {
    .builder-sidebar {
        width: 250px;
    }
    
    .properties-panel {
        width: 250px;
    }
    
    .header-center {
        display: none;
    }
}
